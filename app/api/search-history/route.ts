import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { searchHistory } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';

interface SearchHistoryRequest {
  userId: string;
  searchParams: Record<string, any>;
  propertyIds: string[];
  searchProfileId?: string;
  resultsCount: number;
  searchType?: 'manual' | 'profile' | 'map';
}

/**
 * POST - Track search execution
 */
export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      searchParams,
      propertyIds,
      searchProfileId,
      resultsCount,
      searchType = 'manual'
    }: SearchHistoryRequest = await request.json();

    if (!userId || !searchParams) {
      return NextResponse.json({ 
        error: 'User ID and search parameters are required' 
      }, { status: 400 });
    }

    // Create a hash of search parameters to identify identical searches
    const searchHash = Buffer.from(JSON.stringify(searchParams)).toString('base64');
    
    // Check if this exact search exists for this user
    const existingSearch = await db.select()
      .from(searchHistory)
      .where(eq(searchHistory.userId, userId))
      .orderBy(desc(searchHistory.lastViewedAt))
      .limit(50); // Check last 50 searches for duplicates

    const duplicateSearch = existingSearch.find(search => {
      const existingHash = Buffer.from(search.searchParams).toString('base64');
      return existingHash === searchHash;
    });

    const now = new Date().getTime();

    if (duplicateSearch) {
      // Update existing search - increment view count and update timestamp
      await db.update(searchHistory)
        .set({
          searchViewCount: duplicateSearch.searchViewCount + 1,
          lastViewedAt: now,
          propertyIds: JSON.stringify(propertyIds), // Update with latest results
          resultsCount
        })
        .where(eq(searchHistory.id, duplicateSearch.id));

      return NextResponse.json({
        success: true,
        action: 'updated',
        searchId: duplicateSearch.id,
        viewCount: duplicateSearch.searchViewCount + 1
      });
    } else {
      // Create new search record
      const searchId = `search_${userId}_${Date.now()}`;
      
      await db.insert(searchHistory).values({
        id: searchId,
        userId,
        searchParams: JSON.stringify(searchParams),
        propertyIds: JSON.stringify(propertyIds),
        searchProfileId,
        resultsCount,
        searchType,
        searchViewCount: 1,
        createdAt: now,
        lastViewedAt: now
      });

      return NextResponse.json({
        success: true,
        action: 'created',
        searchId,
        viewCount: 1
      });
    }

  } catch (error) {
    console.error('Error tracking search history:', error);
    return NextResponse.json({
      error: 'Failed to track search history',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET - Retrieve user's search history
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '20');
    const searchType = searchParams.get('searchType');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    let query = db.select()
      .from(searchHistory)
      .where(eq(searchHistory.userId, userId));

    if (searchType) {
      query = query.where(eq(searchHistory.searchType, searchType));
    }

    const userSearchHistory = await query
      .orderBy(desc(searchHistory.lastViewedAt))
      .limit(Math.min(limit, 100));

    const formattedHistory = userSearchHistory.map(search => ({
      id: search.id,
      searchParams: JSON.parse(search.searchParams),
      propertyIds: JSON.parse(search.propertyIds),
      searchProfileId: search.searchProfileId,
      resultsCount: search.resultsCount,
      searchType: search.searchType,
      viewCount: search.searchViewCount,
      createdAt: new Date(search.createdAt).toISOString(),
      lastViewedAt: new Date(search.lastViewedAt).toISOString()
    }));

    return NextResponse.json({
      success: true,
      userId,
      searches: formattedHistory,
      totalSearches: formattedHistory.length
    });

  } catch (error) {
    console.error('Error fetching search history:', error);
    return NextResponse.json({
      error: 'Failed to fetch search history',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
