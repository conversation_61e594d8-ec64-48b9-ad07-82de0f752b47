import { NextRequest, NextResponse } from 'next/server';

const EXTERNAL_API_URL = 'https://api.realestateapi.com/v2/AutoComplete';

/**
 * AutoComplete API endpoint
 * 
 * This endpoint provides address autocomplete functionality by proxying requests
 * to the RealEstateAPI AutoComplete service.
 * 
 * @route POST /api/autocomplete
 * @param {string} search - Minimum 3 characters required
 * @param {string[]} search_types - Default is ['A', 'C'] for address and city search
 * @returns {Object} Response object with address suggestions
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Extract search parameters from request body
    const { search, search_types = ['A', 'C'] } = body;

    // Validate search parameter
    if (!search) {
      return NextResponse.json({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Search query is required',
        results: [],
        data: []
      }, { status: 400 });
    }

    // Validate minimum search length
    if (search.length < 3) {
      return NextResponse.json({
        statusCode: 400,
        statusMessage: 'Bad Request',
        message: 'Search query must be at least 3 characters long',
        results: [],
        data: []
      }, { status: 400 });
    }

    // Get API key from environment
    const API_KEY = process.env.REAL_ESTATE_API_KEY || 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914';
    
    // Prepare headers for the external API request
    const headers = {
      'x-api-key': API_KEY,
      'x-user-id': 'UniqueUserIdentifier',
      'Content-Type': 'application/json'
    };

    // Prepare request body
    const requestBody = JSON.stringify({
      search,
      search_types: Array.isArray(search_types) ? search_types : [search_types]
    });

    console.log(`Making request to ${EXTERNAL_API_URL} with search: ${search}, search_types: ${search_types}`);
    
    // Make request to external API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout
    
    const apiResponse = await fetch(EXTERNAL_API_URL, {
      method: 'POST',
      headers,
      body: requestBody,
      signal: controller.signal
    });

    // Clear timeout
    clearTimeout(timeoutId);

    // Parse response data
    const responseData = await apiResponse.json();
    console.log('Autocomplete API response status:', apiResponse.status);
    console.log('Autocomplete response data:', JSON.stringify(responseData, null, 2));

    // Handle unsuccessful response
    if (!apiResponse.ok) {
      console.error('External API error:', {
        status: apiResponse.status,
        data: responseData
      });
      return NextResponse.json({
        statusCode: apiResponse.status,
        statusMessage: responseData.statusMessage || 'External API Error',
        message: responseData.message || 'Failed to fetch autocomplete suggestions',
        details: responseData,
        results: [],
        data: []
      }, { status: apiResponse.status });
    }

    // Ensure the response has the expected structure
    if (!responseData.results && !responseData.data) {
      // If the API returns a different structure, try to adapt it
      if (Array.isArray(responseData)) {
        // Create a new object with results property
        const newResponse = { results: responseData };
        return NextResponse.json(newResponse);
      } else if (responseData.result && Array.isArray(responseData.result)) {
        responseData.results = responseData.result;
      } else {
        console.log('Unexpected response structure:', Object.keys(responseData));
        responseData.results = [];
      }
    }

    // Process and normalize the results for the frontend
    if (responseData.results && Array.isArray(responseData.results)) {
      responseData.results = responseData.results.map((item: any) => {
        // Ensure each result has the expected fields
        return {
          ...item,
          city: item.city || item.City || '',
          state: item.state || item.State || item.state_code || '',
          zipCode: item.zipCode || item.zip || item.Zip || '',
          title: item.title || item.address || `${item.city || ''}, ${item.state || ''}`,
          searchType: item.searchType || 'C'
        };
      });
    }

    console.log('Processed results count:', responseData.results?.length || responseData.data?.length || 0);

    // Return successful response
    return NextResponse.json(responseData);

  } catch (error: any) {
    // Log detailed error information for troubleshooting
    console.error('Autocomplete API Error:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause
    });
    
    let statusCode = 500;
    let message = 'An unexpected error occurred';
    
    // Handle specific error types
    if (error.name === 'AbortError') {
      statusCode = 408; // Request Timeout
      message = 'Request to external API timed out';
    } else if (error.name === 'SyntaxError') {
      // Handle JSON parsing errors
      message = 'Invalid response format from external API';
    } else if (error.name === 'TypeError') {
      // Handle network errors like CORS, DNS failures, etc.
      message = 'Network error when connecting to external API';
    }
    
    return NextResponse.json({
      statusCode,
      statusMessage: error.name || 'Internal Server Error',
      message,
      timestamp: new Date().toISOString(),
      results: [],
      data: []
    }, { status: statusCode });
  }
}
