import { NextRequest, NextResponse } from 'next/server';

const API_URL = 'https://api.realestateapi.com/v2/PropertyDetailBulk';

/**
 * CMS Property Detail Bulk API
 * 
 * Fetches detailed property information for multiple properties in a single request.
 * Supports up to 1000 property IDs per call.
 * 
 * @route POST /api/property-detail-bulk
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate request parameters
    if (!body.ids || !Array.isArray(body.ids)) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Request must include an array of property IDs'
        },
        { status: 400 }
      );
    }
    
    const { ids } = body;
    
    // Check array length constraints
    if (ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'At least one property ID is required'
        },
        { status: 400 }
      );
    }
    
    if (ids.length > 1000) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Maximum of 1000 property IDs allowed per request'
        },
        { status: 400 }
      );
    }
    
    // Set up request headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log(`Fetching details for ${ids.length} properties from bulk API`);
    
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout for bulk requests
    
    // Make request to external API
    const response = await fetch(API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify({ids}),
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Check for successful response
    if (!response.ok) {
      let errorMessage = `API error: ${response.status} ${response.statusText}`;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch {
        // If we can't parse the error response, use the default message
      }
      
      console.error(errorMessage);
      
      return NextResponse.json(
        {
          success: false,
          statusCode: response.status,
          message: errorMessage
        },
        { status: response.status }
      );
    }
    
    // Parse and return response data
    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      data
    });
    
  } catch (error: any) {
    // Handle request timeout
    if (error.name === 'AbortError') {
      return NextResponse.json(
        {
          success: false,
          statusCode: 504,
          message: 'Property detail bulk API request timed out'
        },
        { status: 504 }
      );
    }
    
    // Handle other errors
    console.error('Property detail bulk API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}