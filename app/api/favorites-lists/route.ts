import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { propertyFavoriteLists, propertyFavorites } from '@/db/schema';
import { eq, and, desc, count } from 'drizzle-orm';

interface CreateListRequest {
  userId: string;
  listName: string;
  listDescription?: string;
  isDefault?: boolean;
}

interface UpdateListRequest {
  listId: string;
  listName?: string;
  listDescription?: string;
}

/**
 * POST - Create new favorites list
 */
export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      listName,
      listDescription,
      isDefault = false
    }: CreateListRequest = await request.json();

    if (!userId || !listName) {
      return NextResponse.json({ 
        error: 'User ID and list name are required' 
      }, { status: 400 });
    }

    // Check if user already has a list with this name
    const [existingList] = await db.select()
      .from(propertyFavoriteLists)
      .where(
        and(
          eq(propertyFavoriteLists.userId, userId),
          eq(propertyFavoriteLists.listName, listName)
        )
      );

    if (existingList) {
      return NextResponse.json({
        error: 'A list with this name already exists'
      }, { status: 409 });
    }

    // If this is set as default, remove default flag from other lists
    if (isDefault) {
      await db.update(propertyFavoriteLists)
        .set({ isDefault: false })
        .where(eq(propertyFavoriteLists.userId, userId));
    }

    // Create new list
    const listId = `list_${userId}_${Date.now()}`;
    
    await db.insert(propertyFavoriteLists).values({
      id: listId,
      userId,
      listName,
      listDescription: listDescription || null,
      isDefault,
      createdAt: new Date().getTime(),
      updatedAt: new Date().getTime()
    });

    return NextResponse.json({
      success: true,
      listId,
      listName,
      message: 'Favorites list created successfully'
    });

  } catch (error) {
    console.error('Error creating favorites list:', error);
    return NextResponse.json({
      error: 'Failed to create favorites list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET - Retrieve user's favorites lists
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ 
        error: 'User ID is required' 
      }, { status: 400 });
    }

    // Get all lists with property counts
    const lists = await db.select({
      list: propertyFavoriteLists,
      propertyCount: count(propertyFavorites.id)
    })
    .from(propertyFavoriteLists)
    .leftJoin(propertyFavorites, eq(propertyFavoriteLists.id, propertyFavorites.listId))
    .where(eq(propertyFavoriteLists.userId, userId))
    .groupBy(propertyFavoriteLists.id)
    .orderBy(desc(propertyFavoriteLists.isDefault), desc(propertyFavoriteLists.createdAt));

    const formattedLists = lists.map(item => ({
      id: item.list.id,
      name: item.list.listName,
      description: item.list.listDescription,
      isDefault: Boolean(item.list.isDefault),
      propertyCount: item.propertyCount,
      createdAt: new Date(item.list.createdAt).toISOString(),
      updatedAt: new Date(item.list.updatedAt).toISOString()
    }));

    return NextResponse.json({
      success: true,
      lists: formattedLists,
      totalLists: formattedLists.length
    });

  } catch (error) {
    console.error('Error fetching favorites lists:', error);
    return NextResponse.json({
      error: 'Failed to fetch favorites lists',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT - Update favorites list
 */
export async function PUT(request: NextRequest) {
  try {
    const {
      listId,
      listName,
      listDescription
    }: UpdateListRequest = await request.json();

    if (!listId) {
      return NextResponse.json({ 
        error: 'List ID is required' 
      }, { status: 400 });
    }

    const updateData: any = {
      updatedAt: new Date().getTime()
    };

    if (listName !== undefined) updateData.listName = listName;
    if (listDescription !== undefined) updateData.listDescription = listDescription;

    await db.update(propertyFavoriteLists)
      .set(updateData)
      .where(eq(propertyFavoriteLists.id, listId));

    return NextResponse.json({
      success: true,
      listId,
      message: 'Favorites list updated successfully'
    });

  } catch (error) {
    console.error('Error updating favorites list:', error);
    return NextResponse.json({
      error: 'Failed to update favorites list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE - Delete favorites list
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const listId = searchParams.get('listId');
    const deleteProperties = searchParams.get('deleteProperties') === 'true';

    if (!listId) {
      return NextResponse.json({ 
        error: 'List ID is required' 
      }, { status: 400 });
    }

    // Check if this is a default list
    const [listToDelete] = await db.select()
      .from(propertyFavoriteLists)
      .where(eq(propertyFavoriteLists.id, listId));

    if (!listToDelete) {
      return NextResponse.json({
        error: 'List not found'
      }, { status: 404 });
    }

    if (listToDelete.isDefault && deleteProperties) {
      return NextResponse.json({
        error: 'Cannot delete default list with properties. Move properties to another list first.'
      }, { status: 400 });
    }

    // Delete all properties in the list if requested
    if (deleteProperties) {
      await db.delete(propertyFavorites)
        .where(eq(propertyFavorites.listId, listId));
    } else {
      // Check if list has properties
      const [hasProperties] = await db.select({ count: count() })
        .from(propertyFavorites)
        .where(eq(propertyFavorites.listId, listId));

      if (hasProperties.count > 0) {
        return NextResponse.json({
          error: `Cannot delete list with ${hasProperties.count} properties. Move properties first or use deleteProperties=true parameter.`
        }, { status: 400 });
      }
    }

    // Delete the list
    await db.delete(propertyFavoriteLists)
      .where(eq(propertyFavoriteLists.id, listId));

    return NextResponse.json({
      success: true,
      message: 'Favorites list deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting favorites list:', error);
    return NextResponse.json({
      error: 'Failed to delete favorites list',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
