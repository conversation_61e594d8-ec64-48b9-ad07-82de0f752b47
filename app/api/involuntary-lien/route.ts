import { NextRequest, NextResponse } from 'next/server';

const INVOLUNTARY_LIEN_API_URL = 'https://api.realestateapi.com/v2/Reports/PropertyLiens';

interface InvoluntaryLienRequest {
  id?: number;
  address?: string;
  zip?: string;
  apn?: string;
  lien_type?: string[];
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

/**
 * Calculate risk level based on liens
 */
function calculateLienRisk(liens: any[], totalAmount: number): string {
  const activeLiens = liens.filter(l => !l.release_date).length;
  
  if (activeLiens === 0) return 'LOW';
  if (activeLiens === 1 && totalAmount < 10000) return 'MODERATE';
  if (activeLiens > 3 || totalAmount > 50000) return 'HIGH';
  if (liens.some(l => l.lien_type === 'TAX')) return 'HIGH';
  
  return 'MODERATE';
}

/**
 * Involuntary Lien API
 * 
 * Searches for involuntary liens on properties including tax liens,
 * mechanic's liens, HOA liens, and judgment liens.
 * 
 * @route POST /api/involuntary-lien
 */
export async function POST(request: NextRequest) {
  try {
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    let body: InvoluntaryLienRequest;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    if (!body.id && !body.address && !body.zip && !body.apn) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Request must include at least one identifier: id, address, zip, or apn'
        },
        { status: 400 }
      );
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log('Searching for involuntary liens');
    
    const response = await fetch(INVOLUNTARY_LIEN_API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Involuntary Lien API error: ${response.status} - ${errorText}`);
      return NextResponse.json(
        {
          success: false,
          statusCode: response.status,
          message: `Involuntary Lien API error: ${response.statusText}`
        },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    const liens = data.liens || data.results || [];
    
    const categorizedLiens = {
      taxLiens: liens.filter((l: any) => l.lien_type === 'TAX'),
      mechanicsLiens: liens.filter((l: any) => l.lien_type === 'MECHANIC'),
      hoaLiens: liens.filter((l: any) => l.lien_type === 'HOA'),
      judgmentLiens: liens.filter((l: any) => l.lien_type === 'JUDGMENT'),
      otherLiens: liens.filter((l: any) => !['TAX', 'MECHANIC', 'HOA', 'JUDGMENT'].includes(l.lien_type))
    };
    
    const totalLienAmount = liens.reduce((sum: number, lien: any) => sum + (lien.amount || 0), 0);
    const oldestLien = liens.reduce((oldest: any, lien: any) => {
      if (!oldest || new Date(lien.filing_date) < new Date(oldest.filing_date)) {
        return lien;
      }
      return oldest;
    }, null);
    
    return NextResponse.json({
      success: true,
      data: {
        propertyId: data.propertyId || body.id,
        address: data.address || body.address,
        totalLiens: liens.length,
        totalLienAmount,
        oldestLienDate: oldestLien?.filing_date,
        liensByType: categorizedLiens,
        liens: liens.map((lien: any) => ({
          lienId: lien.lien_id,
          type: lien.lien_type,
          amount: lien.amount,
          filingDate: lien.filing_date,
          releaseDate: lien.release_date,
          status: lien.status || (lien.release_date ? 'RELEASED' : 'ACTIVE'),
          creditor: lien.creditor,
          description: lien.description,
          caseNumber: lien.case_number,
          bookPage: lien.book_page,
          priority: lien.priority
        })),
        riskLevel: calculateLienRisk(liens, totalLienAmount),
        investmentImpact: {
          cloudsTitle: liens.some((l: any) => !l.release_date),
          requiresResolution: liens.filter((l: any) => !l.release_date).length > 0,
          estimatedClearanceCost: totalLienAmount * 1.1, // Add 10% for legal fees
          section8Impact: liens.some((l: any) => l.lien_type === 'TAX') ? 'HIGH' : 'MODERATE'
        }
      }
    });
    
  } catch (error: any) {
    console.error('Involuntary Lien API error:', error);
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}