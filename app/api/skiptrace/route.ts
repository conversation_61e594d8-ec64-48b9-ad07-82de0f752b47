import { NextRequest, NextResponse } from 'next/server';

const SKIPTRACE_API_URL = 'https://api.realestateapi.com/v1/SkipTrace';

interface SkipTraceRequest {
  // Property identification
  id?: string;
  address?: string;
  
  // Address parts
  house?: string;
  street?: string;
  city?: string;
  state?: string;
  zip?: string;
  
  // Owner information for skip tracing
  firstName?: string;
  lastName?: string;
  ownerName?: string;
  
  // Additional parameters
  includePhoneNumbers?: boolean;
  includeEmails?: boolean;
  includeRelatives?: boolean;
  includePreviousAddresses?: boolean;
}

/**
 * SkipTrace API
 * 
 * Provides skip tracing services to find contact information for property owners.
 * This includes phone numbers, email addresses, and other contact details.
 * 
 * @route POST /api/skiptrace
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key
    const API_KEY = process.env.REAL_ESTATE_API_KEY;
    if (!API_KEY) {
      console.error('Error: REAL_ESTATE_API_KEY is not set in environment variables.');
      return NextResponse.json(
        { 
          success: false,
          statusCode: 500,
          message: 'API key is not configured.' 
        },
        { status: 500 }
      );
    }
    
    // Parse request body
    let body: SkipTraceRequest;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Invalid JSON in request body'
        },
        { status: 400 }
      );
    }
    
    // Validate request - must have either property ID or complete address
    if (!body.id && !body.address && !body.house) {
      return NextResponse.json(
        {
          success: false,
          statusCode: 400,
          message: 'Request must include either a property ID, full address, or address parts'
        },
        { status: 400 }
      );
    }
    
    // For SkipTrace API, complete address is required (address+city+state+zip)
    if (!body.id && !body.address) {
      // Using address parts - validate all required fields are present
      if (!body.city || !body.state || !body.zip) {
        return NextResponse.json(
          {
            success: false,
            statusCode: 400,
            message: 'SkipTrace API requires complete address: city, state, and zip are all required when using address parts'
          },
          { status: 400 }
        );
      }
    }
    
    // Build request parameters
    const skipTraceParams: Record<string, any> = {};
    
    // Property identification
    if (body.id) {
      skipTraceParams.id = body.id;
    } else if (body.address) {
      skipTraceParams.address = body.address;
    } else {
      // Address parts
      if (body.house) skipTraceParams.house = body.house;
      if (body.street) skipTraceParams.street = body.street;
      if (body.city) skipTraceParams.city = body.city;
      if (body.state) skipTraceParams.state = body.state;
      if (body.zip) skipTraceParams.zip = body.zip;
    }
    
    // Owner information if provided
    if (body.firstName) skipTraceParams.firstName = body.firstName;
    if (body.lastName) skipTraceParams.lastName = body.lastName;
    if (body.ownerName) skipTraceParams.ownerName = body.ownerName;
    
    // Additional options (default to true for comprehensive results)
    skipTraceParams.includePhoneNumbers = body.includePhoneNumbers !== false;
    skipTraceParams.includeEmails = body.includeEmails !== false;
    skipTraceParams.includeRelatives = body.includeRelatives !== false;
    skipTraceParams.includePreviousAddresses = body.includePreviousAddresses !== false;
    
    // Set up headers
    const headers = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY
    };
    
    console.log('Executing SkipTrace for:', body.id || body.address || 'address parts');
    
    // Set up request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout
    
    try {
      // Make request to SkipTrace API
      const response = await fetch(SKIPTRACE_API_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(skipTraceParams),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Check for successful response
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`SkipTrace API error: ${response.status} - ${errorText}`);
        
        return NextResponse.json(
          {
            success: false,
            statusCode: response.status,
            message: `SkipTrace API error: ${response.statusText}`
          },
          { status: response.status }
        );
      }
      
      // Parse response
      const data = await response.json();
      
      // Return formatted response
      return NextResponse.json({
        success: true,
        data: {
          // Property information
          propertyId: data.propertyId || data.id,
          address: data.address,
          
          // Owner information
          owner: {
            fullName: data.ownerName || data.owner_name,
            firstName: data.firstName || data.first_name,
            lastName: data.lastName || data.last_name,
            middleName: data.middleName || data.middle_name,
            suffix: data.suffix
          },
          
          // Contact information
          contactInfo: {
            phoneNumbers: data.phoneNumbers || data.phone_numbers || [],
            emails: data.emails || data.email_addresses || [],
            currentAddress: data.currentAddress || data.current_address,
            mailingAddress: data.mailingAddress || data.mailing_address
          },
          
          // Additional skip trace data
          relatives: data.relatives || [],
          previousAddresses: data.previousAddresses || data.previous_addresses || [],
          
          // Demographics
          demographics: {
            age: data.age,
            dateOfBirth: data.dateOfBirth || data.dob,
            gender: data.gender,
            maritalStatus: data.maritalStatus || data.marital_status
          },
          
          // Social media and online presence
          socialMedia: data.socialMedia || data.social_media || [],
          
          // Data quality indicators
          dataQuality: {
            matchConfidence: data.matchConfidence || data.match_confidence,
            lastUpdated: data.lastUpdated || data.last_updated,
            dataSource: data.dataSource || data.data_source
          },
          
          // Raw data for debugging
          _raw: data
        },
        executionTime: data.executionTimeMS || null
      });
      
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      // Handle timeout
      if (error.name === 'AbortError') {
        return NextResponse.json(
          {
            success: false,
            statusCode: 504,
            message: 'SkipTrace API request timed out'
          },
          { status: 504 }
        );
      }
      
      throw error;
    }
    
  } catch (error: any) {
    console.error('SkipTrace API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        statusCode: 500,
        message: error.message || 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
} 