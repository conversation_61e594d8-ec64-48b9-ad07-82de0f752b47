import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { propertyFavorites, propertyFavoriteLists } from '@/db/schema';
import { eq, and, desc } from 'drizzle-orm';

interface AddFavoriteRequest {
  userId: string;
  propertyId: string;
  listId?: string;
  notes?: string;
  tags?: string[];
}

interface UpdateFavoriteRequest {
  favoriteId: string;
  notes?: string;
  tags?: string[];
}

/**
 * POST - Add property to favorites list
 */
export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      propertyId,
      listId,
      notes,
      tags
    }: AddFavoriteRequest = await request.json();

    if (!userId || !propertyId) {
      return NextResponse.json({ 
        error: 'User ID and Property ID are required' 
      }, { status: 400 });
    }

    let targetListId = listId;

    // If no listId provided, use user's default list or create one
    if (!targetListId) {
      const [defaultList] = await db.select()
        .from(propertyFavoriteLists)
        .where(
          and(
            eq(propertyFavoriteLists.userId, userId),
            eq(propertyFavoriteLists.isDefault, true)
          )
        );

      if (defaultList) {
        targetListId = defaultList.id;
      } else {
        // Create default list
        const newListId = `list_${userId}_default_${Date.now()}`;
        await db.insert(propertyFavoriteLists).values({
          id: newListId,
          userId,
          listName: 'My Favorites',
          listDescription: 'Default favorites list',
          isDefault: true,
          createdAt: new Date().getTime(),
          updatedAt: new Date().getTime()
        });
        targetListId = newListId;
      }
    }

    // Check if property already exists in this list
    const [existingFavorite] = await db.select()
      .from(propertyFavorites)
      .where(
        and(
          eq(propertyFavorites.listId, targetListId),
          eq(propertyFavorites.propertyId, propertyId)
        )
      );

    if (existingFavorite) {
      return NextResponse.json({
        error: 'Property already exists in this favorites list',
        favoriteId: existingFavorite.id
      }, { status: 409 });
    }

    // Check list capacity (max 100 properties per list)
    const listProperties = await db.select()
      .from(propertyFavorites)
      .where(eq(propertyFavorites.listId, targetListId));

    if (listProperties.length >= 100) {
      return NextResponse.json({
        error: 'Maximum 100 properties allowed per favorites list'
      }, { status: 400 });
    }

    // Add property to favorites
    const favoriteId = `fav_${targetListId}_${propertyId}_${Date.now()}`;
    
    await db.insert(propertyFavorites).values({
      id: favoriteId,
      listId: targetListId,
      propertyId,
      notes: notes || null,
      tags: tags ? JSON.stringify(tags) : null,
      addedAt: new Date().getTime()
    });

    return NextResponse.json({
      success: true,
      favoriteId,
      listId: targetListId,
      propertyId,
      message: 'Property added to favorites'
    });

  } catch (error) {
    console.error('Error adding property to favorites:', error);
    return NextResponse.json({
      error: 'Failed to add property to favorites',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET - Retrieve user's favorite properties
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const listId = searchParams.get('listId');
    const limit = parseInt(searchParams.get('limit') || '100');

    if (!userId && !listId) {
      return NextResponse.json({ 
        error: 'Either User ID or List ID is required' 
      }, { status: 400 });
    }

    let favorites = [];

    if (listId) {
      // Get favorites from specific list
      favorites = await db.select({
        favorite: propertyFavorites,
        list: propertyFavoriteLists
      })
      .from(propertyFavorites)
      .leftJoin(propertyFavoriteLists, eq(propertyFavorites.listId, propertyFavoriteLists.id))
      .where(eq(propertyFavorites.listId, listId))
      .orderBy(desc(propertyFavorites.addedAt))
      .limit(limit);
    } else if (userId) {
      // Get all favorites for user across all lists
      favorites = await db.select({
        favorite: propertyFavorites,
        list: propertyFavoriteLists
      })
      .from(propertyFavorites)
      .leftJoin(propertyFavoriteLists, eq(propertyFavorites.listId, propertyFavoriteLists.id))
      .where(eq(propertyFavoriteLists.userId, userId))
      .orderBy(desc(propertyFavorites.addedAt))
      .limit(limit);
    }

    const formattedFavorites = favorites.map(item => ({
      id: item.favorite.id,
      propertyId: item.favorite.propertyId,
      notes: item.favorite.notes,
      tags: item.favorite.tags ? JSON.parse(item.favorite.tags) : [],
      addedAt: new Date(item.favorite.addedAt).toISOString(),
      list: {
        id: item.list?.id,
        name: item.list?.listName,
        description: item.list?.listDescription,
        isDefault: Boolean(item.list?.isDefault)
      }
    }));

    return NextResponse.json({
      success: true,
      favorites: formattedFavorites,
      totalFavorites: formattedFavorites.length
    });

  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json({
      error: 'Failed to fetch favorites',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT - Update favorite property (notes/tags)
 */
export async function PUT(request: NextRequest) {
  try {
    const {
      favoriteId,
      notes,
      tags
    }: UpdateFavoriteRequest = await request.json();

    if (!favoriteId) {
      return NextResponse.json({ 
        error: 'Favorite ID is required' 
      }, { status: 400 });
    }

    const updateData: any = {};
    if (notes !== undefined) updateData.notes = notes;
    if (tags !== undefined) updateData.tags = JSON.stringify(tags);

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ 
        error: 'No update data provided' 
      }, { status: 400 });
    }

    await db.update(propertyFavorites)
      .set(updateData)
      .where(eq(propertyFavorites.id, favoriteId));

    return NextResponse.json({
      success: true,
      favoriteId,
      message: 'Favorite updated successfully'
    });

  } catch (error) {
    console.error('Error updating favorite:', error);
    return NextResponse.json({
      error: 'Failed to update favorite',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE - Remove property from favorites
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const favoriteId = searchParams.get('favoriteId');
    const propertyId = searchParams.get('propertyId');
    const listId = searchParams.get('listId');

    if (!favoriteId && !(propertyId && listId)) {
      return NextResponse.json({ 
        error: 'Either favorite ID or (property ID + list ID) is required' 
      }, { status: 400 });
    }

    if (favoriteId) {
      // Delete by favorite ID
      await db.delete(propertyFavorites)
        .where(eq(propertyFavorites.id, favoriteId));
    } else {
      // Delete by property ID + list ID
      await db.delete(propertyFavorites)
        .where(
          and(
            eq(propertyFavorites.propertyId, propertyId!),
            eq(propertyFavorites.listId, listId!)
          )
        );
    }

    return NextResponse.json({
      success: true,
      message: 'Property removed from favorites'
    });

  } catch (error) {
    console.error('Error removing favorite:', error);
    return NextResponse.json({
      error: 'Failed to remove favorite',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
