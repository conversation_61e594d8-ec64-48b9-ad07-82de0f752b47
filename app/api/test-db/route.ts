import { NextResponse } from 'next/server'
import { testConnection, initializeTables } from '@/lib/database'

export async function GET() {
  try {
    // Test the connection
    const connectionSuccess = await testConnection()
    
    if (!connectionSuccess) {
      return NextResponse.json(
        { error: 'Failed to connect to database' },
        { status: 500 }
      )
    }
    
    // Initialize tables
    const tablesSuccess = await initializeTables()
    
    if (!tablesSuccess) {
      return NextResponse.json(
        { error: 'Failed to initialize database tables' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful and tables initialized',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      { error: 'Database test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
